# 日志查看教程

本文档介绍如何在flyXxtt2项目中查看各种类型的日志，帮助开发者进行问题排查和系统监控。

## 项目架构概述

项目基于Docker容器运行，包含以下主要服务：
- `flyxxtt2-laravel.test-1`: Laravel应用容器
- `flyxxtt2-mysql-1`: MySQL数据库容器
- `flyxxtt2-redis-1`: Redis缓存容器
- `flyxxtt2-mailpit-1`: 邮件测试容器

## 1. Docker容器日志

### 1.1 查看Laravel应用容器日志

```bash
# 查看最新的10条日志
docker logs flyxxtt2-laravel.test-1 --tail 10

# 查看最新的50条日志
docker logs flyxxtt2-laravel.test-1 --tail 50

# 实时跟踪日志（类似tail -f）
docker logs flyxxtt2-laravel.test-1 --follow

# 查看特定时间段的日志
docker logs flyxxtt2-laravel.test-1 --since "2025-07-30T20:00:00"
docker logs flyxxtt2-laravel.test-1 --since "1h"  # 最近1小时
docker logs flyxxtt2-laravel.test-1 --since "30m" # 最近30分钟

# 查看所有日志
docker logs flyxxtt2-laravel.test-1

# 将日志输出到文件
docker logs flyxxtt2-laravel.test-1 > laravel_logs.txt
```

### 1.2 查看其他容器日志

```bash
# MySQL数据库日志
docker logs flyxxtt2-mysql-1 --tail 20

# Redis缓存日志
docker logs flyxxtt2-redis-1 --tail 20

# Mailpit邮件服务日志
docker logs flyxxtt2-mailpit-1 --tail 20
```

## 2. Laravel应用日志

### 2.1 日志文件位置

Laravel应用日志存储在：`storage/logs/laravel.log`

### 2.2 Windows PowerShell中查看日志

```powershell
# 查看最新20条日志
Get-Content storage/logs/laravel.log -Tail 20

# 查看最新50条日志
Get-Content storage/logs/laravel.log -Tail 50

# 实时监控日志变化
Get-Content storage/logs/laravel.log -Wait -Tail 10

# 查看所有日志
Get-Content storage/logs/laravel.log

# 搜索包含特定关键词的日志
Get-Content storage/logs/laravel.log | Select-String "ERROR"
Get-Content storage/logs/laravel.log | Select-String "password"
Get-Content storage/logs/laravel.log | Select-String "SQL"
```

### 2.3 在Docker容器内查看日志

```bash
# 进入Laravel容器
docker exec -it flyxxtt2-laravel.test-1 bash

# 在容器内使用Linux命令查看日志
tail -f /var/www/html/storage/logs/laravel.log
tail -20 /var/www/html/storage/logs/laravel.log
grep "ERROR" /var/www/html/storage/logs/laravel.log
```

## 3. 日志级别说明

Laravel使用Monolog日志库，支持以下日志级别（按严重程度排序）：

1. **EMERGENCY**: 系统不可用，需要立即处理
2. **ALERT**: 必须立即采取行动
3. **CRITICAL**: 严重错误条件
4. **ERROR**: 运行时错误，不需要立即处理但应该记录和监控
5. **WARNING**: 警告信息，不是错误但可能表示问题
6. **NOTICE**: 正常但重要的信息
7. **INFO**: 一般信息性消息
8. **DEBUG**: 详细的调试信息

## 4. 日志格式解析

Laravel日志的典型格式：
```
[时间戳] 环境.级别: 消息内容 {"上下文数据"}
```

例如：
```
[2025-05-31 15:24:58] production.INFO: Changing password for game account {"login":"tester","raw_password":"йцу123"}
```

- `[2025-05-31 15:24:58]`: 时间戳
- `production`: 应用环境
- `INFO`: 日志级别
- `Changing password for game account`: 日志消息
- `{"login":"tester",...}`: 上下文数据（JSON格式）

## 5. 常用日志查看场景

### 5.1 查看错误日志

```powershell
# 查看所有错误级别的日志
Get-Content storage/logs/laravel.log | Select-String "ERROR"

# 查看最近的错误日志
Get-Content storage/logs/laravel.log | Select-String "ERROR" | Select-Object -Last 10
```

### 5.2 查看SQL相关日志

```powershell
# 查看SQL执行日志
Get-Content storage/logs/laravel.log | Select-String "SQL"

# 查看密码修改相关日志
Get-Content storage/logs/laravel.log | Select-String "password"
```

### 5.3 查看特定时间段的日志

```powershell
# 查看今天的日志
$today = Get-Date -Format "yyyy-MM-dd"
Get-Content storage/logs/laravel.log | Select-String $today
```

## 6. 日志轮转和清理

### 6.1 查看日志文件大小

```powershell
# 查看日志文件大小
Get-ChildItem storage/logs/laravel.log | Select-Object Name, Length

# 以MB为单位显示
Get-ChildItem storage/logs/laravel.log | Select-Object Name, @{Name="SizeMB";Expression={[math]::Round($_.Length/1MB,2)}}
```

### 6.2 清理旧日志

```bash
# 在容器内清空日志文件（谨慎操作）
docker exec -it flyxxtt2-laravel.test-1 bash -c "echo '' > /var/www/html/storage/logs/laravel.log"

# 或者使用Laravel命令清理日志
docker exec -it flyxxtt2-laravel.test-1 php artisan log:clear
```

## 7. 实时监控和调试

### 7.1 实时监控多个日志源

```bash
# 同时监控Docker日志和Laravel日志
# 终端1：监控Docker日志
docker logs flyxxtt2-laravel.test-1 --follow

# 终端2：监控Laravel应用日志
Get-Content storage/logs/laravel.log -Wait -Tail 10
```

### 7.2 使用Laravel Telescope（如果安装）

如果项目安装了Laravel Telescope，可以通过Web界面查看：
- 访问：`http://localhost/telescope`
- 可以查看请求、异常、查询、邮件等详细信息

## 8. 示例场景

### 示例1：排查用户登录问题

```powershell
# 查看最近的认证相关日志
Get-Content storage/logs/laravel.log | Select-String -Pattern "(login|auth|password)" | Select-Object -Last 20
```

### 示例2：排查数据库连接问题

```bash
# 查看MySQL容器日志
docker logs flyxxtt2-mysql-1 --tail 30

# 查看Laravel中的数据库错误
Get-Content storage/logs/laravel.log | Select-String -Pattern "(database|connection|mysql)" | Select-Object -Last 10
```

### 示例3：排查性能问题

```powershell
# 查看慢查询或性能相关日志
Get-Content storage/logs/laravel.log | Select-String -Pattern "(slow|performance|timeout)" | Select-Object -Last 15
```

### 示例4：查看API请求日志

```bash
# 查看Docker容器的HTTP访问日志
docker logs flyxxtt2-laravel.test-1 --tail 50 | grep "GET\|POST\|PUT\|DELETE"
```

### 示例5：监控实时错误

```powershell
# 实时监控错误日志
Get-Content storage/logs/laravel.log -Wait | Select-String "ERROR"
```

## 9. 日志分析工具推荐

### 9.1 命令行工具
- `grep`: 文本搜索
- `awk`: 文本处理
- `sed`: 流编辑器
- `jq`: JSON处理（处理结构化日志）

### 9.2 图形化工具
- **Kibana + Elasticsearch**: 企业级日志分析
- **Grafana**: 日志可视化
- **Seq**: .NET生态的日志分析工具
- **Papertrail**: 云端日志管理

## 10. 最佳实践

1. **定期检查日志**：建议每天查看错误日志
2. **设置日志轮转**：避免日志文件过大
3. **监控关键指标**：设置错误率告警
4. **结构化日志**：使用JSON格式便于分析
5. **敏感信息保护**：避免在日志中记录密码等敏感信息
6. **日志备份**：重要日志应该备份保存

## 11. 故障排查流程

1. **确定问题范围**：是应用问题还是基础设施问题？
2. **查看Docker容器状态**：`docker ps -a`
3. **检查容器日志**：`docker logs [container_name]`
4. **查看应用日志**：检查Laravel日志文件
5. **分析错误堆栈**：定位具体错误位置
6. **检查相关服务**：数据库、Redis等依赖服务
7. **验证修复**：修复后持续监控日志

---

**注意事项**：
- 在生产环境中，避免使用`--follow`长时间监控，可能影响性能
- 定期清理日志文件，避免磁盘空间不足
- 敏感信息（如密码）不应出现在日志中，如发现应立即处理
- 使用适当的日志级别，避免过多的DEBUG信息影响性能
