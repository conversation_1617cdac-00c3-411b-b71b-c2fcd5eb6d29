<?php

use Carbon\CarbonInterval;

if (! function_exists('crest')) {

    function readInt($file)
    {
        $b4 = ord(fgetc($file));
        $b3 = ord(fgetc($file));
        $b2 = ord(fgetc($file));
        $b1 = ord(fgetc($file));

        return ($b1 << 24) | ($b2 << 16) | ($b3 << 8) | $b4;
    }

    function readShort($file)
    {
        $b2 = ord(fgetc($file));
        $b1 = ord(fgetc($file));

        return ($b1 << 8) | $b2;
    }

    function emptyCrest()
    {
        return '';
    }

    function crest($type, $id, $crest)
    {
        $cacheTime = 3600;

        $pathCrest = storage_path('/app/public');
        $path      = $pathCrest . DIRECTORY_SEPARATOR . $type;

        $filePath  = $id . '.png';

        if(!is_dir($path))
        {
            if(!mkdir($path, 0777, TRUE))
            {
                return '';
            }
        }

        if (!is_file($path . DIRECTORY_SEPARATOR . $filePath) || (time() - filemtime($path . DIRECTORY_SEPARATOR . $filePath) >= $cacheTime))
        {
            $rnd_file = tmpfile();
            fwrite($rnd_file, $crest);
            fseek($rnd_file, 0);

            $file = &$rnd_file; //fopen($filename,'rb');
            $dds = fread($file, 4);

            if ($dds !== 'DDS ') {
                return emptyCrest();
            }

            $hdrSize = readInt($file);
            $hdrFlags = readInt($file);
            $imgHeight = readInt($file) - 4;
            $imgWidth = readInt($file);
            $imgPitch = readShort($file);

            fseek($file, 84);

            $dxt1 = fread($file, 4);

            if ($dxt1 !== 'DXT1') {
                return emptyCrest();
            }

            fseek($file, 128);

            //header ("Content-type: image/png");
            $img = imagecreatetruecolor($imgWidth, $imgHeight);

            for ($y = -1; $y < $imgHeight / 4; $y++) {
                for ($x = 0; $x < $imgWidth / 4; $x++) {
                    $color0_16 = readShort($file);
                    $color1_16 = readShort($file);
                    $r0 = ($color0_16 >> 11) << 3;
                    $g0 = (($color0_16 >> 5) & 63) << 2;
                    $b0 = ($color0_16 & 31) << 3;
                    $r1 = ($color1_16 >> 11) << 3;
                    $g1 = (($color1_16 >> 5) & 63) << 2;
                    $b1 = ($color1_16 & 31) << 3;
                    $color0_32 = imagecolorallocate($img, $r0, $g0, $b0);
                    $color1_32 = imagecolorallocate($img, $r1, $g1, $b1);
                    $color01_32 = imagecolorallocate($img, $r0 / 2 + $r1 / 2, $g0 / 2 + $g1 / 2, $b0 / 2 + $b1 / 2);
                    $black = imagecolorallocate($img, 0, 0, 0);
                    $data = readInt($file);
                    for ($yy = 0; $yy < 4; $yy++) {
                        for ($xx = 0; $xx < 4; $xx++) {
                            $bb = $data & 3;
                            $data = $data >> 2;
                            switch ($bb) {
                                case 0:
                                    $c = $color0_32;
                                    break;
                                case 1:
                                    $c = $color1_32;
                                    break;
                                case 2:
                                    $c = $color01_32;
                                    break;
                                default:
                                    $c = $black;
                                    break;
                            }
                            imagesetpixel($img, $x * 4 + $xx, $y * 4 + $yy, $c);
                        }
                    }
                }
            }

            imagepng($img, $path . DIRECTORY_SEPARATOR . $filePath);
            imagedestroy($img);
        }

        $getCrestUrl = asset('storage/'. $type .'/' . $filePath);

        return $getCrestUrl;
    }
}


if (! function_exists('class_id')) {
    function class_id($class_id)
    {
        $classList = [
            0 => [
                'name' => '人类战士',
                'race' => 0,
            ],
            1 => [
                'name' => '斗士',
                'race' => 0,
            ],
            2 => [
                'name' => '剑斗士',
                'race' => 0,
            ],
            3 => [
                'name' => '佣兵',
                'race' => 0,
            ],
            4 => [
                'name' => '骑士',
                'race' => 0,
            ],
            5 => [
                'name' => '圣骑士',
                'race' => 0,
            ],
            6 => [
                'name' => '暗骑士',
                'race' => 0,
            ],
            7 => [
                'name' => '盗贼',
                'race' => 0,
            ],
            8 => [
                'name' => '宝藏猎人',
                'race' => 0,
            ],
            9 => [
                'name' => '鹰眼',
                'race' => 0,
            ],
            10 => [
                'name' => '人类法师',
                'race' => 0,
            ],
            11 => [
                'name' => '巫师',
                'race' => 0,
            ],
            12 => [
                'name' => '术士',
                'race' => 0,
            ],
            13 => [
                'name' => '死灵法师',
                'race' => 0,
            ],
            14 => [
                'name' => '法魔',
                'race' => 0,
            ],
            15 => [
                'name' => '牧师',
                'race' => 0,
            ],
            16 => [
                'name' => '主教',
                'race' => 0,
            ],
            17 => [
                'name' => '先知',
                'race' => 0,
            ],
            88 => [
                'name' => '角斗士',
                'race' => 0,
            ],
            89 => [
                'name' => '勇士',
                'race' => 0,
            ],
            90 => [
                'name' => '神骑士',
                'race' => 0,
            ],
            91 => [
                'name' => '死亡骑士',
                'race' => 0,
            ],
            92 => [
                'name' => '神射手',
                'race' => 0,
            ],
            93 => [
                'name' => '冒险家',
                'race' => 0,
            ],
            94 => [
                'name' => '大法师',
                'race' => 0,
            ],
            95 => [
                'name' => '灵魂捕获者',
                'race' => 0,
            ],
            96 => [
                'name' => '秘术之王',
                'race' => 0,
            ],
            97 => [
                'name' => '红衣主教',
                'race' => 0,
            ],
            98 => [
                'name' => '大祭司',
                'race' => 0,
            ],
            18 => [
                'name' => '精灵战士',
                'race' => 1,
            ],
            19 => [
                'name' => '精灵骑士',
                'race' => 1,
            ],
            20 => [
                'name' => '圣殿骑士',
                'race' => 1,
            ],
            21 => [
                'name' => '剑术诗人',
                'race' => 1,
            ],
            22 => [
                'name' => '精灵巡守',
                'race' => 1,
            ],
            23 => [
                'name' => '大地行者',
                'race' => 1,
            ],
            24 => [
                'name' => '银月游侠',
                'race' => 1,
            ],
            25 => [
                'name' => '精灵法师',
                'race' => 1,
            ],
            26 => [
                'name' => '精灵巫师',
                'race' => 1,
            ],
            27 => [
                'name' => '咒术诗人',
                'race' => 1,
            ],
            28 => [
                'name' => '元素使',
                'race' => 1,
            ],
            29 => [
                'name' => '神使',
                'race' => 1,
            ],
            30 => [
                'name' => '长老',
                'race' => 1,
            ],
            99 => [
                'name' => '伊娃圣骑士',
                'race' => 1,
            ],
            100 => [
                'name' => '吟游剑士',
                'race' => 1,
            ],
            101 => [
                'name' => '风骑士',
                'race' => 1,
            ],
            102 => [
                'name' => '月光守护者',
                'race' => 1,
            ],
            103 => [
                'name' => '神圣诗人',
                'race' => 1,
            ],
            104 => [
                'name' => '元素大师',
                'race' => 1,
            ],
            105 => [
                'name' => '伊娃圣徒',
                'race' => 1,
            ],
            31 => [
                'name' => '黑暗精灵战士',
                'race' => 2,
            ],
            32 => [
                'name' => '沼泽骑士',
                'race' => 2,
            ],
            33 => [
                'name' => '席琳骑士',
                'race' => 2,
            ],
            34 => [
                'name' => '剑刃舞者',
                'race' => 2,
            ],
            35 => [
                'name' => '暗杀者',
                'race' => 2,
            ],
            36 => [
                'name' => '深渊行者',
                'race' => 2,
            ],
            37 => [
                'name' => '暗影游侠',
                'race' => 2,
            ],
            38 => [
                'name' => '黑暗精灵法师',
                'race' => 2,
            ],
            39 => [
                'name' => '黑暗巫师',
                'race' => 2,
            ],
            40 => [
                'name' => '狂咒术士',
                'race' => 2,
            ],
            41 => [
                'name' => '暗影召唤士',
                'race' => 2,
            ],
            42 => [
                'name' => '席琳神使',
                'race' => 2,
            ],
            43 => [
                'name' => '席林长老',
                'race' => 2,
            ],
            106 => [
                'name' => '席琳圣骑士',
                'race' => 2,
            ],
            107 => [
                'name' => '幽灵舞者',
                'race' => 2,
            ],
            108 => [
                'name' => '幽灵猎人',
                'race' => 2,
            ],
            109 => [
                'name' => '幽灵守护者',
                'race' => 2,
            ],
            110 => [
                'name' => '暴风狂啸者',
                'race' => 2,
            ],
            111 => [
                'name' => '幽灵大师',
                'race' => 2,
            ],
            112 => [
                'name' => '席琳圣徒',
                'race' => 2,
            ],
            44 => [
                'name' => '半兽人战士',
                'race' => 3,
            ],
            45 => [
                'name' => '半兽人突击者',
                'race' => 3,
            ],
            46 => [
                'name' => '破坏者',
                'race' => 3,
            ],
            47 => [
                'name' => '半兽人武者',
                'race' => 3,
            ],
            48 => [
                'name' => '暴君',
                'race' => 3,
            ],
            49 => [
                'name' => '半兽人法师',
                'race' => 3,
            ],
            50 => [
                'name' => '半兽人巫医',
                'race' => 3,
            ],
            51 => [
                'name' => '霸主',
                'race' => 3,
            ],
            52 => [
                'name' => '战狂',
                'race' => 3,
            ],
            113 => [
                'name' => '巨人',
                'race' => 3,
            ],
            114 => [
                'name' => '武道家',
                'race' => 3,
            ],
            115 => [
                'name' => '统治者',
                'race' => 3,
            ],
            116 => [
                'name' => '毁灭使者',
                'race' => 3,
            ],
            53 => [
                'name' => '矮人战士',
                'race' => 4,
            ],
            54 => [
                'name' => '收集者',
                'race' => 4,
            ],
            55 => [
                'name' => '赏金猎人',
                'race' => 4,
            ],
            56 => [
                'name' => '工匠',
                'race' => 4,
            ],
            57 => [
                'name' => '战争工匠',
                'race' => 4,
            ],
            117 => [
                'name' => '探索者',
                'race' => 4,
            ],
            118 => [
                'name' => '巨匠',
                'race' => 4,
            ],
            123 => [
                'name' => '男-暗天使战士',
                'race' => 5,
            ],
            124 => [
                'name' => '女-暗天使战士',
                'race' => 5,
            ],
            125 => [
                'name' => '突袭护卫',
                'race' => 5,
            ],
            126 => [
                'name' => '狙击护卫',
                'race' => 5,
            ],
            127 => [
                'name' => '狂战士',
                'race' => 5,
            ],
            128 => [
                'name' => '男-破魂者',
                'race' => 5,
            ],
            129 => [
                'name' => '女-破魂者',
                'race' => 5,
            ],
            130 => [
                'name' => '战弩手',
                'race' => 5,
            ],
            131 => [
                'name' => '末日剑神',
                'race' => 5,
            ],
            132 => [
                'name' => '男-灵魂猎人',
                'race' => 5,
            ],
            133 => [
                'name' => '女-灵魂猎人',
                'race' => 5,
            ],
            134 => [
                'name' => '幻术师',
                'race' => 5,
            ],
            135 => [
                'name' => '巡守',
                'race' => 5,
            ],
            136 => [
                'name' => '审判者',
                'race' => 5,
            ],
            139 => [
                'name' => 'Sigel Knight',
                'race' => 5,
            ],
            140 => [
                'name' => 'Tyrr Warrior',
                'race' => 5,
            ],
            141 => [
                'name' => 'Othell Rogue',
                'race' => 5,
            ],
            142 => [
                'name' => 'Yul Archer',
                'race' => 5,
            ],
            143 => [
                'name' => 'Feoh Wizard',
                'race' => 5,
            ],
            144 => [
                'name' => 'Iss Enchanter',
                'race' => 5,
            ],
            145 => [
                'name' => 'Wynn Summoner',
                'race' => 5,
            ],
            146 => [
                'name' => 'Aeore Healer',
                'race' => 5,
            ],
            147 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            148 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            149 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            150 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            151 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            152 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            153 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            154 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],
            155 => [
                'name' => 'DUMMY',
                'race' => 5,
            ],

        ];

        return $classList[$class_id]['name'];
    }




if (! function_exists('class_list')) {
    function class_list()
    {
        return [
            0 => [
                'name' => '人类战士',
                'race' => 0,
            ],
            1 => [
                'name' => '斗士',
                'race' => 0,
            ],
            2 => [
                'name' => '剑斗士',
                'race' => 0,
            ],
            3 => [
                'name' => '佣兵',
                'race' => 0,
            ],
            4 => [
                'name' => '骑士',
                'race' => 0,
            ],
            5 => [
                'name' => '圣骑士',
                'race' => 0,
            ],
            6 => [
                'name' => '暗骑士',
                'race' => 0,
            ],
            7 => [
                'name' => '盗贼',
                'race' => 0,
            ],
            8 => [
                'name' => '宝藏猎人',
                'race' => 0,
            ],
            9 => [
                'name' => '鹰眼',
                'race' => 0,
            ],
            10 => [
                'name' => '人类法师',
                'race' => 0,
            ],
            11 => [
                'name' => '巫师',
                'race' => 0,
            ],
            12 => [
                'name' => '术士',
                'race' => 0,
            ],
            13 => [
                'name' => '死灵法师',
                'race' => 0,
            ],
            14 => [
                'name' => '法魔',
                'race' => 0,
            ],
            15 => [
                'name' => '牧师',
                'race' => 0,
            ],
            16 => [
                'name' => '主教',
                'race' => 0,
            ],
            17 => [
                'name' => '先知',
                'race' => 0,
            ],
            88 => [
                'name' => '角斗士',
                'race' => 0,
            ],
            89 => [
                'name' => '勇士',
                'race' => 0,
            ],
            90 => [
                'name' => '神骑士',
                'race' => 0,
            ],
            91 => [
                'name' => '死亡骑士',
                'race' => 0,
            ],
            92 => [
                'name' => '神射手',
                'race' => 0,
            ],
            93 => [
                'name' => '冒险家',
                'race' => 0,
            ],
            94 => [
                'name' => '大法师',
                'race' => 0,
            ],
            95 => [
                'name' => '灵魂捕获者',
                'race' => 0,
            ],
            96 => [
                'name' => '秘术之王',
                'race' => 0,
            ],
            97 => [
                'name' => '红衣主教',
                'race' => 0,
            ],
            98 => [
                'name' => '大祭司',
                'race' => 0,
            ],
            18 => [
                'name' => '精灵战士',
                'race' => 1,
            ],
            19 => [
                'name' => '精灵骑士',
                'race' => 1,
            ],
            20 => [
                'name' => '圣殿骑士',
                'race' => 1,
            ],
            21 => [
                'name' => '剑术诗人',
                'race' => 1,
            ],
            22 => [
                'name' => '精灵巡守',
                'race' => 1,
            ],
            23 => [
                'name' => '大地行者',
                'race' => 1,
            ],
            24 => [
                'name' => '银月游侠',
                'race' => 1,
            ],
            25 => [
                'name' => '精灵法师',
                'race' => 1,
            ],
            26 => [
                'name' => '精灵巫师',
                'race' => 1,
            ],
            27 => [
                'name' => '咒术诗人',
                'race' => 1,
            ],
            28 => [
                'name' => '元素使',
                'race' => 1,
            ],
            29 => [
                'name' => '神使',
                'race' => 1,
            ],
            30 => [
                'name' => '长老',
                'race' => 1,
            ],
            99 => [
                'name' => '伊娃圣骑士',
                'race' => 1,
            ],
            100 => [
                'name' => '吟游剑士',
                'race' => 1,
            ],
            101 => [
                'name' => '风骑士',
                'race' => 1,
            ],
            102 => [
                'name' => '月光守护者',
                'race' => 1,
            ],
            103 => [
                'name' => '神圣诗人',
                'race' => 1,
            ],
            104 => [
                'name' => '元素大师',
                'race' => 1,
            ],
            105 => [
                'name' => '伊娃圣徒',
                'race' => 1,
            ],
            31 => [
                'name' => '黑暗精灵战士',
                'race' => 2,
            ],
            32 => [
                'name' => '沼泽骑士',
                'race' => 2,
            ],
            33 => [
                'name' => '席琳骑士',
                'race' => 2,
            ],
            34 => [
                'name' => '剑刃舞者',
                'race' => 2,
            ],
            35 => [
                'name' => '暗杀者',
                'race' => 2,
            ],
            36 => [
                'name' => '深渊行者',
                'race' => 2,
            ],
            37 => [
                'name' => '暗影游侠',
                'race' => 2,
            ],
            38 => [
                'name' => '黑暗精灵法师',
                'race' => 2,
            ],
            39 => [
                'name' => '黑暗巫师',
                'race' => 2,
            ],
            40 => [
                'name' => '狂咒术士',
                'race' => 2,
            ],
            41 => [
                'name' => '暗影召唤士',
                'race' => 2,
            ],
            42 => [
                'name' => '席琳神使',
                'race' => 2,
            ],
            43 => [
                'name' => '席林长老',
                'race' => 2,
            ],
            106 => [
                'name' => '席琳圣骑士',
                'race' => 2,
            ],
            107 => [
                'name' => '幽灵舞者',
                'race' => 2,
            ],
            108 => [
                'name' => '幽灵猎人',
                'race' => 2,
            ],
            109 => [
                'name' => '幽灵守护者',
                'race' => 2,
            ],
            110 => [
                'name' => '暴风狂啸者',
                'race' => 2,
            ],
            111 => [
                'name' => '幽灵大师',
                'race' => 2,
            ],
            112 => [
                'name' => '席琳圣徒',
                'race' => 2,
            ],
            44 => [
                'name' => '半兽人战士',
                'race' => 3,
            ],
            45 => [
                'name' => '半兽人突击者',
                'race' => 3,
            ],
            46 => [
                'name' => '破坏者',
                'race' => 3,
            ],
            47 => [
                'name' => '半兽人武者',
                'race' => 3,
            ],
            48 => [
                'name' => '暴君',
                'race' => 3,
            ],
            49 => [
                'name' => '半兽人法师',
                'race' => 3,
            ],
            50 => [
                'name' => '半兽人巫医',
                'race' => 3,
            ],
            51 => [
                'name' => '霸主',
                'race' => 3,
            ],
            52 => [
                'name' => '战狂',
                'race' => 3,
            ],
            113 => [
                'name' => '巨人',
                'race' => 3,
            ],
            114 => [
                'name' => '武道家',
                'race' => 3,
            ],
            115 => [
                'name' => '统治者',
                'race' => 3,
            ],
            116 => [
                'name' => '毁灭使者',
                'race' => 3,
            ],
            53 => [
                'name' => '矮人战士',
                'race' => 4,
            ],
            54 => [
                'name' => '收集者',
                'race' => 4,
            ],
            55 => [
                'name' => '赏金猎人',
                'race' => 4,
            ],
            56 => [
                'name' => '工匠',
                'race' => 4,
            ],
            57 => [
                'name' => '战争工匠',
                'race' => 4,
            ],
            117 => [
                'name' => '探索者',
                'race' => 4,
            ],
            118 => [
                'name' => '巨匠',
                'race' => 4,
            ],
            123 => [
                'name' => '男-暗天使战士',
                'race' => 5,
            ],
            124 => [
                'name' => '女-暗天使战士',
                'race' => 5,
            ],
            125 => [
                'name' => '突袭护卫',
                'race' => 5,
            ],
            126 => [
                'name' => '狙击护卫',
                'race' => 5,
            ],
            127 => [
                'name' => '狂战士',
                'race' => 5,
            ],
            128 => [
                'name' => '男-破魂者',
                'race' => 5,
            ],
            129 => [
                'name' => '女-破魂者',
                'race' => 5,
            ],
            130 => [
                'name' => '战弩手',
                'race' => 5,
            ],
            131 => [
                'name' => '末日剑神',
                'race' => 5,
            ],
            132 => [
                'name' => '男-灵魂猎人',
                'race' => 5,
            ],
            133 => [
                'name' => '女-灵魂猎人',
                'race' => 5,
            ],
            134 => [
                'name' => '幻术师',
                'race' => 5,
            ],
            135 => [
                'name' => '巡守',
                'race' => 5,
            ],
            136 => [
                'name' => '审判者',
                'race' => 5,
            ],
            139 => [
                'name' => 'Sigel Knight',
                'race' => 5,
            ],
            140 => [
                'name' => 'Tyrr Warrior',
                'race' => 5,
            ],
            141 => [
                'name' => 'Othell Rogue',
                'race' => 5,
            ],
            142 => [
                'name' => 'Yul Archer',
                'race' => 5,
            ],
            143 => [
                'name' => 'Feoh Wizard',
                'race' => 5,
            ],
            144 => [
                'name' => 'Iss Enchanter',
                'race' => 5,
            ],
            145 => [
                'name' => 'Wynn Summoner',
                'race' => 5,
            ],
            146 => [
                'name' => 'Aeore Healer',
                'race' => 5,
            ],
        ];
    }
}


if (! function_exists('race_name')) {
    function race_name($raceId)
    {
        $raceList = [
            0 => 'Human',
            1 => 'Elf',
            2 => 'Dark elf',
            3 => 'Orc',
            4 => 'Dwarf',
            5 => 'Kamael',
        ];

        return $raceList[$raceId] ?? 'Unknown';
    }
}



    if (! function_exists('skillIcon')) {
        function skillIcon($skill_id)
        {
            $pathSkillIcon = storage_path('app/public/json/Skillgrp.json');
            $skillParse = json_decode(file_get_contents($pathSkillIcon), true);

            foreach ($skillParse as $skill) {
                if ( $skill['skill_id'] == $skill_id) {
                    $icon = asset('storage/icons/' . $skill["icon_name"] . '.png');
                    return $icon;
                }
            }
        }
    }

    if (! function_exists('skillName')) {
        function skillName($skill_id)
        {
            $pathSkillName = storage_path('app/public/json/SkillName-e.json');
            $skillParse = json_decode(file_get_contents($pathSkillName), true);

            foreach ($skillParse as $skill) {
                if ( $skill['id'] == $skill_id) {
                    $name = $skill["name"];
                    $desc = $skill["description"];

                    $totalNameDesc = $name . "<br>" . '<span>' . $desc . '</span>';
                    return $totalNameDesc;
                }
            }
        }
    }

    if (! function_exists('itemIcon')) {
        function itemIcon($item_id)
        {
            $pathItemIcon = storage_path('app/public/json/H5_Armorgrp.json');
            $itemsParse = json_decode(file_get_contents($pathItemIcon), true);

            foreach ($itemsParse as $item) {
                if ( $item['id'] == $item_id) {
                    $icon = asset('storage/icons/' . $item["icon"] . '.png');
                    return $icon;
                }
            }
        }
    }

    if (! function_exists('itemEtcItemIcon')) {
        function itemEtcItemIcon($item_id)
        {
            $pathEtcItemItcon = storage_path('app/public/json/H5_EtcItemgrp.json');
            $itemsEtcItemParse = json_decode(file_get_contents($pathEtcItemItcon), true);

            foreach ($itemsEtcItemParse as $item) {
                if ( $item['id'] == $item_id) {
                    $icon = asset('storage/icons/' . $item["icon"] . '.png');
                    return $icon;
                }
            }
        }
    }
    if (! function_exists('WeapongrpItem')) {
        function WeapongrpItem($item_id)
        {
            $pathWeapongrpItcon = storage_path('app/public/json/H5_Weapongrp.json');
            $itemsWeaponParse = json_decode(file_get_contents($pathWeapongrpItcon), true);

            foreach ($itemsWeaponParse as $item) {
                if ( $item['id'] == $item_id) {
                    $icon = asset('storage/icons/' . $item["icon"] . '.png');
                    return $icon;
                }
            }
        }
    }

    if (! function_exists('itemName')) {
        function itemName($item_id)
        {
            $pathItemName = storage_path('app/public/json/ItemName-e.json');
            $itemNameParse = json_decode(file_get_contents($pathItemName), true);

            foreach ($itemNameParse as $item) {

                if ( $item['id'] == $item_id) {
                    $name = $item["name"];
                    $add_name = $item["add_name"];
                    $desc = $item["description"];

                    $totalNameDesc = $name . $add_name;
                    return $totalNameDesc;
                }
            }
        }
    }

    if (! function_exists('itemDesc')) {
        function itemDesc($item_id)
        {
            $pathItemName = storage_path('app/public/json/ItemName-e.json');
            $itemNameParse = json_decode(file_get_contents($pathItemName), true);

            foreach ($itemNameParse as $item) {

                if ( $item['id'] == $item_id) {
                    $desc = $item["description"];

                    $totalNameDesc = $desc;
                    return $totalNameDesc;
                }
            }
        }
    }

    if (! function_exists('item_icon')) {
        function item_icon($item_id)
        {
            return Cache::remember('item_icon_' . $item_id, now()->addMinutes(config('app.bonus_system.bonus_cache_item')), function () use ($item_id) {
                $item_icon = Cache::remember('item_icon_' . $item_id, now()->addMinutes(config('app.bonus_system.bonus_cache_item')), function () use ($item_id) {
                    return itemIcon($item_id);
                });

                if ($item_icon == '') {
                    $item_icon = Cache::remember('weapongrp_icon_' . $item_id, now()->addMinutes(config('app.bonus_system.bonus_cache_item')), function () use ($item_id) {
                        return WeapongrpItem($item_id);
                    });
                }

                if ($item_icon == '') {
                    $item_icon = Cache::remember('item_etc_icon_' . $item_id, now()->addMinutes(config('app.bonus_system.bonus_cache_item')), function () use ($item_id) {
                        return itemEtcItemIcon($item_id);
                    });
                }

                if ($item_icon == '') {
                    $item_icon = Cache::remember('item_icon_' . $item_id, now()->addMinutes(config('app.bonus_system.bonus_cache_item')), function () use ($item_id) {
                        return itemIcon($item_id);
                    });
                }

                return $item_icon;
            });
        }
    }

    if (! function_exists('online_time')) {
        function online_time($online_time)
        {
            $online_time = CarbonInterval::seconds($online_time)->cascade()->forHumans();

            return $online_time;
        }
    }

    if (! function_exists('passHash')) {
        function passHash($get_password_hash)
        {
            if('whirpool' === 'whirpool') {
                $get_password_hash = base64_encode(hash('whirlpool', $get_password_hash, true));
            }

            return $get_password_hash;
        }
    }

    if (!function_exists('base_url')) {
        function base_url($path) {
            $template = config('app.template');
            return asset("template/{$template}/{$path}");
        }
    }

    if (!function_exists('template_view')) {
        function template_view($view) {
            $template = config('app.template');
            return "template.{$template}.views.{$view}";
        }
    }

    function gameservers(): string
    {
        $startTime = microtime(true);
        \Log::info('[gameservers] 开始获取游戏服务器配置');

        // Определяем текущий тип серверов
        $serverType = Config::get('app.l2server_type', 'java'); // По умолчанию 'java'
        \Log::info('[gameservers] 服务器类型', ['type' => $serverType]);

        // Получаем список серверов в зависимости от типа
        $servers = $serverType === 'pts'
            ? Config::get('app.pts_servers', [])
            : Config::get('app.java_servers', []);
        \Log::info('[gameservers] 服务器列表', ['servers' => array_keys($servers), 'count' => count($servers)]);

        // Получаем сервер из сессии
        $sessionStart = microtime(true);
        $serverKey = Session::get('server');
        $sessionTime = (microtime(true) - $sessionStart) * 1000;
        \Log::info('[gameservers] Session::get()耗时', ['time_ms' => round($sessionTime, 2), 'server_key' => $serverKey]);

        // Проверяем, существует ли сервер в конфигурации
        if (!array_key_exists($serverKey, $servers)) {
            \Log::warning('[gameservers] 服务器不存在，使用默认服务器', ['requested' => $serverKey]);
            // Если сервер не существует, сбрасываем на сервер по умолчанию
            $defaultServerKey = array_key_first($servers); // Первый сервер из списка

            $sessionPutStart = microtime(true);
            Session::put('server', $defaultServerKey); // Сохраняем новый сервер в сессию
            $sessionPutTime = (microtime(true) - $sessionPutStart) * 1000;
            \Log::info('[gameservers] Session::put()耗时', ['time_ms' => round($sessionPutTime, 2), 'default_key' => $defaultServerKey]);

            $totalTime = (microtime(true) - $startTime) * 1000;
            \Log::info('[gameservers] 函数总耗时(默认)', ['time_ms' => round($totalTime, 2), 'result' => $defaultServerKey]);
            return $defaultServerKey;
        }

        $totalTime = (microtime(true) - $startTime) * 1000;
        \Log::info('[gameservers] 函数总耗时(正常)', ['time_ms' => round($totalTime, 2), 'result' => $serverKey]);
        return $serverKey;
    }

    // Vaganth PTS Castle, castle table
    if (!function_exists('getCastleNameById')) {
        /**
         * Получить название замка по ID.
         *
         * @param int $id
         * @return string|null
         */
        function getCastleNameById(int $id): ?string
        {
            // Список замков из Lineage 2
            $castles = [
                1 => 'Gludio',
                2 => 'Dion',
                3 => 'Giran',
                4 => 'Oren',
                5 => 'Aden',
                6 => 'Innadril',
                7 => 'Goddard',
                8 => 'Rune',
                9 => 'Schuttgart',
            ];

            // Возвращаем название замка по ID или null, если ID не найден
            return $castles[$id] ?? null;
        }
    }

    if (!function_exists('encryptPTS')) {
        /**
         * Кастомное шифрование строки.
         *
         * @param string $str Исходная строка для шифрования.
         * @return string Зашифрованная строка в формате 0x... для MSSQL.
         */
        function encryptPTS(string $str): string
        {
            $key = [];
            $dst = [];
            $i = 0;

            // Убедимся, что строка имеет минимальную длину 16 символов
            $str = str_pad($str, 16, "\0");

            $nBytes = strlen($str);
            while ($i < $nBytes) {
                $i++;
                $key[$i] = ord(substr($str, $i - 1, 1));
                $dst[$i] = $key[$i];
            }

            // Алгоритм остается без изменений
            $rslt = $key[1] + $key[2]*256 + $key[3]*65536 + $key[4]*16777216;
            $one = $rslt * 213119 + 2529077;
            $one = $one - intval($one / 4294967296) * 4294967296;

            $rslt = $key[5] + $key[6]*256 + $key[7]*65536 + $key[8]*16777216;
            $two = $rslt * 213247 + 2529089;
            $two = $two - intval($two / 4294967296) * 4294967296;

            $rslt = $key[9] + $key[10]*256 + $key[11]*65536 + $key[12]*16777216;
            $three = $rslt * 213203 + 2529589;
            $three = $three - intval($three / 4294967296) * 4294967296;

            $rslt = $key[13] + $key[14]*256 + $key[15]*65536 + $key[16]*16777216;
            $four = $rslt * 213821 + 2529997;
            $four = $four - intval($four / 4294967296) * 4294967296;

            // Присвоение значений ключам $key
            $key[4] = intval($one / 16777216);
            $key[3] = intval(($one - $key[4] * 16777216) / 65536);
            $key[2] = intval(($one - $key[4] * 16777216 - $key[3] * 65536) / 256);
            $key[1] = intval(($one - $key[4] * 16777216 - $key[3] * 65536 - $key[2] * 256));

            $key[8] = intval($two / 16777216);
            $key[7] = intval(($two - $key[8] * 16777216) / 65536);
            $key[6] = intval(($two - $key[8] * 16777216 - $key[7] * 65536) / 256);
            $key[5] = intval(($two - $key[8] * 16777216 - $key[7] * 65536 - $key[6] * 256));

            $key[12] = intval($three / 16777216);
            $key[11] = intval(($three - $key[12] * 16777216) / 65536);
            $key[10] = intval(($three - $key[12] * 16777216 - $key[11] * 65536) / 256);
            $key[9] = intval(($three - $key[12] * 16777216 - $key[11] * 65536 - $key[10] * 256));

            $key[16] = intval($four / 16777216);
            $key[15] = intval(($four - $key[16] * 16777216) / 65536);
            $key[14] = intval(($four - $key[16] * 16777216 - $key[15] * 65536) / 256);
            $key[13] = intval(($four - $key[16] * 16777216 - $key[15] * 65536 - $key[14] * 256));

            $dst[1] = $dst[1] ^ $key[1];

            $i = 1;
            while ($i < 16) {
                $i++;
                $dst[$i] = $dst[$i] ^ $dst[$i - 1] ^ $key[$i];
            }

            $i = 0;
            while ($i < 16) {
                $i++;
                if ($dst[$i] == 0) {
                    $dst[$i] = 102;
                }
            }

            $encrypt = "0x";
            $i = 0;
            while ($i < 16) {
                $i++;
                if ($dst[$i] < 16) {
                    $encrypt .= "0" . dechex($dst[$i]);
                } else {
                    $encrypt .= dechex($dst[$i]);
                }
            }

            return $encrypt;
        }
    }

    if (!function_exists('hashPassword')) {
        /**
         * Хэшировать пароль на основе конфигурации.
         *
         * @param string $password
         * @return string
         */
        function hashPassword(string $password)
        {
            // Получаем метод хэширования из конфигурации
            $hashMethod = config('app.password_hash', 'whirlpool');

            // Выбираем метод хэширования
            switch ($hashMethod) {
                case 'sha1':
                    return base64_encode(sha1($password, true));
                case 'hAuth':
                    return encryptPTS($password); // Пример реализации для hAuth
                case 'whirlpool':
                    return base64_encode(hash('whirlpool', $password, true)); // Пример реализации для hAuth
                case 'bcrypt':
                    return password_hash($password, PASSWORD_BCRYPT);
                case 'md5':
                    return base64_encode(md5($password, true)); // Реализация md5 с Base64
                case 'sunrise':
                    return base64_encode(pack('H*', sha1($password)));
                case 'acis':
                    $hash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 10]);
                    return str_replace('$2y$10$', '$2a$10$', $hash);
                default:
                    return base64_encode(hash('whirlpool', $password, true));
            }
        }
    }
}
