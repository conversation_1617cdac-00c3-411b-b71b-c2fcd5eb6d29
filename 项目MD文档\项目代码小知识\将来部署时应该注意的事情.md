# 将来部署时应该注意的事情

## Laravel 存储符号链接问题

### 问题描述
Laravel 应用使用存储系统来管理文件，特别是游戏图标等静态资源。这些文件存储在 `storage/app/public` 目录中，但需要通过 Web 服务器的 `/storage` URL 路径访问。

### 必须执行的命令
在每次部署后，必须创建存储符号链接：

```bash
# Laravel Artisan 命令（推荐）
php artisan storage:link
```

### 不同部署环境的注意事项

#### 1. Windows 服务器直接部署
```powershell
# 进入项目根目录
cd C:\path\to\your\project

# 创建符号链接
php artisan storage:link

# 验证符号链接是否创建成功
Test-Path "public\storage"
Get-Item "public\storage" | Select-Object LinkType, Target
```

#### 2. Docker 部署（推荐）
```dockerfile
# 在 Dockerfile 中或容器启动脚本中添加
RUN php artisan storage:link
```

或者在 docker-compose.yml 的启动命令中：
```yaml
services:
  app:
    # ... 其他配置
    command: >
      sh -c "php artisan storage:link &&
             php artisan serve --host=0.0.0.0 --port=8000"
```

#### 3. Laravel Sail 开发环境
```bash
# 在容器内执行
./vendor/bin/sail artisan storage:link
```

### 验证部署是否成功
部署完成后，检查以下 URL 是否能正常访问：
- `http://your-domain/storage/icons/etc_scroll_of_enchant_weapon_i02.png`
- `http://your-domain/storage/icons/etc_spell_shot_white_i00.png`
- `http://your-domain/storage/icons/etc_lesser_potion_green_i00.png`

### 相关文件路径
- **存储配置**: `config/filesystems.php`
- **图标文件**: `storage/app/public/icons/`
- **符号链接**: `public/storage` -> `storage/app/public`
- **助手函数**: `app/Helpers/Helper.php` (包含 `skillIcon()`, `itemIcon()` 等函数)

### 常见问题排查
1. **404 错误**: 检查符号链接是否存在
2. **权限问题**: 确保 Web 服务器有读取权限
3. **路径问题**: 确认 `APP_URL` 环境变量设置正确

### 自动化部署脚本建议
```bash
#!/bin/bash
# deploy.sh

# 1. 拉取最新代码
git pull origin main

# 2. 安装依赖
composer install --no-dev --optimize-autoloader

# 3. 创建存储符号链接
php artisan storage:link

# 4. 清除缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 5. 运行数据库迁移（如果需要）
php artisan migrate --force

echo "部署完成！"
```
